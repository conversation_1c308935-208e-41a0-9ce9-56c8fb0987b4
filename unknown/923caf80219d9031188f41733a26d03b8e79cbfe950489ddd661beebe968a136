class MyText {
  static const String mood = 'MOOD';
  static const String name = 'Name';
  static const String box = 'BOX';
  static const String hiThere = 'Hi, There';
  static const String howAbout =
      'How about a quickly enter to see all the freatures!';
  static const String emailAddress = 'Email address or username';
  static const String password = 'Password';
  static const String forgotYourPass = 'Forgot your password?';
  static const String rememberMe = 'Remember me';
  static const String login = 'Log In';
  static const String or = 'Or';
  static const String facebook = 'Continue with Facebook';
  static const String apple = 'Continue with Apple';
  static const String google = 'Continue with Google';
  static const String dontHaveAccount = 'Dont have an account?';
  static const String signupFor = 'Sign up';
  static const String audioDesc = 'Audio Description';
  static const String investor = 'Investor Relations';
  static const String privacy = 'Privacy';
  static const String contactUs = 'Contact Us';
  static const String helpCenter = 'Help Center';
  static const String jobs = 'Jobs';
  static const String legalNotices = 'Legal Notices';
  static const String doNotSell =
      'Do Not Sell or Share My Personal Information';
  static const String giftCards = 'Gift Cards';
  static const String netflixShop = 'Netflix Shop';
  static const String cookie = 'Cookie Preferences';
  static const String ad = 'Ad Choices';
  static const String mediaCenter = 'Media Center';
  static const String terms = 'Terms of Use';
  static const String corporate = 'Corporate Information';
  static const String serviceCode = 'Service Code';
  static const String c1997 = '© 1997 - 2024 Netflix, Inc.';
  static const String home = 'Home';
  static const String suggest = 'Suggest';
  static const String signup = 'Sign Up';
  static const String mostWatched = 'Most Watched  For This Week';
  static const String johnWick = 'John Wick 3 :\nParabellum';
  static const String johnWickCaption =
      'John Wick is on the run after killing a member of the international assassins guild, and with a 14 million price tag on his head, he is the target of hit men and women everywhere.';
  static const String addToPlaylist = 'Add To Playlist';
  static const String moreInfo = 'More Info';
  static const String tvShows = 'TV Shows';
  static const String movies = 'Movies';
  static const String newPopular = 'New & Popular';
  static const String myList = 'My List';
  static const String browseByLanguage = 'Browse by Languages';
  static const String topMovies = 'Top 10 Movies';
  static const String topSeries = 'Top 10 Series';
  static const String topActors = 'Top 10 Actors';
  static const String newRelease = 'New Release';
  static const String usa2018 = 'USA, 2018';
  static const String spiderMan = 'Spider-Man';
  static const String animation = 'Animation,';
  static const String action = 'Action,';
  static const String adventure = 'Adventure';
  static const String dunkirk = 'Dunkirk';
  static const String usa2017 = 'USA, 2017';
  static const String drama = 'Drama';
  static const String history = 'History';
  static const String pickedForYou = 'Picked For You';
  static const String meetOthers = 'Meet Others With Same Taste';
  static const String c2025 = '© 2025 Mood box, Inc.';
  static const String friends = 'Friends';
  static const String watched = 'Watched';
  static const String filters = 'Filters';
  static const String genres = 'Genres';
  static const String titleType = 'Title type';
  static const String releaseYear = 'Release year';
  static const String imdbRating = 'IMDb rating';
  static const String youAnd = 'You and this list';
  static const String keywords = 'Keywords';
  static const String sortBy = 'Sort by:';
  static const String listOrder = 'List order';
  static const String all = 'All';
  static const String searchMoodBox = 'Search MOOD BOX';
  static const String kungFuPanda = 'Kung Fu Panda 4';
  static const String nineNoble =
      'Nine noble families fight for control over the lands of Westeros, while an ancient enemy returns after being dormant for a millennia.';
  static const String director = 'Director:';
  static const String sianHeder = 'Sian Heder';
  static const String stars = 'Stars:';
  static const String emiliaJones = 'Emilia Jones, Marlee Matlin, Troy Kotsur';
  static const String votes = 'Votes: 162,764';
  static const String rate = 'Rate';
  static const String sianHeders =
      "Sian Heder's funny, feel-good family drama won the U.S. Dramatic Special Jury Award after its virtual premiere at Sundance in 2021, and went on to delight audiences via streaming after a major Apple TV+ purchase. I was thrilled to see it win Best Picture at the 2022 Oscars, plus bring home a Best Adapted Screenplay award for Heder and historic Best Actor win for deaf actor Troy Kotsur. Warm, fuzzy feelings all around – prepare to laugh AND cry! — Hannah P.";
  static const String myFavorite = 'My Favorite';
  static const String iWant = 'I want to watch';
  static const String myFavoriteActor = 'My favorite\nactor';
  static const String strangerThings = 'Stranger Things';
  static const String usa2016 = 'USA, 2016 - Current';
  static const String usa2005 = 'USA, 2005';
  static const String batmanBegins = 'Batman Begins';
  static const String horror = 'Horror';
  static const String findFriends = 'Find Friends';
  static const String patrickBatman = 'Patrick Batman';
  static const String add = 'Add';
  static const String myFriends = 'My Friends';
  static const String profile = 'Profile';
  static const String following = 'Following';
  static const String followers = 'Followers';
  static const String watchList = 'WatchList';
  static const String likes = 'Likes';
  static const String favQuote = 'Fav Quote ';
  static const String wellIThink =
      'Well, I think testimony that can put a boy into the electric chair *should* be that accurate.';
  static const String twelveAngryMen = '12 Angry men';
  static const String dunePartTwo = 'Dune: Part Two';
  static const String trailer = 'Trailer';
  static const String genre = 'Genre';
  static const String paulAtreides =
      'Paul Atreides unites with Chani and the Fremen while seeking revenge against the conspirators who destroyed his family.';
  static const String reviews = 'Reviews';
  static const String denisVilleneuve = 'Denis Villeneuve';
  static const String timotheeChalamet = 'Timothée Chalamet';
  static const String oneKUserReviews = '1K User Reviews';
  static const String fiveHundredCriticReviews = '500 Critic Reviews';
  static const String seventyNineMetascore = '79 Metascore';
  static const String seeImdb = 'SEE IMDB';
  static const String addToWatchlist = 'Add to Watchlist';
  static const String photos = 'Photos';
  static const String seeAll = 'See all';
  static const String cast = 'Cast';
  static const String zendaya = 'Zendaya';
  static const String rebeccaFerguson = 'Rebecca Ferguson';
  static const String paulAtreidesCharacter = 'Paul Atreides';
  static const String chani = 'Chani';
  static const String jessica = 'Jessica';
  static const String javierBardem = 'Javier Bardem';
  static const String stilgar = 'Stilgar';
  static const String joshBrolin = 'Josh Brolin';
  static const String gurneyHalleck = 'Gurney Halleck';
  static const String austinButler = 'Austin Butler';
  static const String feydRautha = 'Feyd-Rautha';
  static const String edit = 'Edit';
  static const String userReviews = 'User Reviews';
  static const String oneOfTheGreatest =
      'One Of The Greatest Sequel Ever Made, Dune: Part Two Was Easily The Best Films Of The Year So Far';
  static const String username = 'username';
  static const String inTheQuiet =
      "In the quiet embrace of ink and page, a story unfolded, timeless and sage, through the lens of a filmmaker's artistry, its essence soared, a masterpiece for all to see, I think Denis Villeneuve has just made the most visually stunning epic story of a movie that's ever been made, the most powerful story of a movie ever been told in the last 20 years, there has been no movies with this scale resulting in not just a piece of a film no more but a piece of art, it's what Infinity War and Endgame looks like...";
  static const String helpful = 'Helpful';
  static const String twentyFeb2024 = '20 Feb 2024';
  static const String storyline = 'Storyline';
  static const String summary = 'Summary';
  static const String paulAtreidesSummary =
      'Paul Atreides unites with Chani and the Fremen while on a warpath of revenge against the conspirators who destroyed his family. Facing a choice between the love of his life and the fate of the known universe, he endeavors to prevent a terrible future only he can foresee.';
  static const String warnerBrosPictures = '—Warner Bros. Pictures';
  static const String desertPlanet = 'desert planet';
  static const String secondPart = 'second part';
  static const String dune = 'dune';
  static const String desert = 'desert';
  static const String exploration = 'exploration';
  static const String oneHundredElevenMore = '111 more';
  static const String taglines = 'Taglines';
  static const String longLive = 'Long live the fighters.';
  static const String parentsGuide = 'Parents Guide';
  static const String ratedPg13 =
      'Rated PG-13 for sequences of strong violence, some suggestive material and brief strong language.';
  static const String details = 'Details';
  static const String releaseDate = 'Release date';
  static const String marchOne2024 = 'March 1, 2024 (United States)';
  static const String countriesOfOrigin = 'Countries of origin';
  static const String unitedStates = 'United Stated';
  static const String canada = 'Canada';
  static const String uae = 'UAE';
  static const String hungary = 'Hungary';
  static const String newZealand = 'New Zealand';
  static const String italy = 'Italy';
  static const String jordan = 'Jordan';
  static const String gambia = 'Gambia';
  static const String language = 'Language';
  static const String english = 'English';
  static const String boxOffice = 'Box office';
  static const String budget = 'Budget';
  static const String openingWeekend = 'Opening weekend US & Canada';
  static const String grossUsCanada = 'Gross US & Canada';
  static const String grossWorldwide = 'Gross worldwide';
  static const String moreLikeThis = 'More like this';
  static const String starWars =
      'Star Wars: Episode V - The Empire Strikes Back';
  static const String spiderManAcross = 'Spider-Man: Across the Spider-Verse';
  static const String interstellar = 'Interstellar';
  static const String arrival = 'Arrival';
  static const String inception = 'Inception';
  static const String forYourTaste = 'For Your Taste';
  static const String choseFiveMovies =
      'Chose 5 Movies or Tv Series To Have A Recommend According On Your Taste ';
  static const String weRecommend = 'We Recommend This Movies / Tv Series';
  static const String theFallOfTheHouse =
      'THE FALL OF THE HOUSE OF USHER:LIMITED SERIES (2023)';
  static const String lessonsInChemistry =
      'LESSONS IN CHEMISTRY: SEASON 1 (2023)';
  static const String goosebumps = 'GOOSEBUMPS: SEASON 1 (2023)';
  static const String loki = 'LOKI: SEASON 2 (2023)';
  static const String fellowTravelers = 'FELLOW TRAVELERS: SEASON 1 (2023)';
  static const String genV = 'GEN V: SEASON 1 (2023)';
  static const String ourFlagMeansDeath =
      'OUR FLAG MEANS DEATH: SEASON 2 (2023)';
  static const String thirtyCoins = '30 COINS: SEASON 2 (2023)';
  static const String theOrder = 'THE ORDER: SEASON 2 (2020)';
  static const String midnightMass = 'MIDNIGHT MASS: LIMITED SERIES (2021)';
  static const String rickAndMorty = 'RICK AND MORTY: SEASON 7 (2023)';
  static const String wolfLikeMe = 'WOLF LIKE ME: SEASON 2 (2023)';
  static const String theHauntingOfHillHouse =
      'THE HAUNTING OF HILL HOUSE: MINISERIES (2018)';
  static const String getGotti = 'GET GOTTI: SEASON 1 (2023)';
  static const String theMorningShow = 'THE MORNING SHOW: SEASON 3 (2023)';
  static const String theMidnightClub = 'THE MIDNIGHT CLUB: SEASON 1 (2022)';
  static const String searchOrStart = 'Search or start new chat';
  static const String private = 'PRIVATE';
  static const String allChats = 'ALL CHATS';
  static const String zilan = 'Zilan';
  static const String thankYouVeryMuch = 'Thank you very much, I am wai ...';
  static const String shehnaz = 'Shehnaz';
  static const String client = 'Client';
  static const String whatTime = 'What time are we there? ';
  static const String mueez = 'Mueez';
  static const String youIWill = 'You: I will send you the work file';
  static const String hasnain = 'Hasnain';
  static const String saleem = 'Saleem';
  static const String hiThereHowAreYou = 'Hi there, How are you?';
  static const String waitingForYour =
      'Waiting for your reply. As I have to go back\nsoon. I have to travel long distance.';
  static const String hiIAm =
      'Hi, I am coming there in few\nminutes. Please wait!! I am in taxi right now.';
  static const String thankYouVeryMuchStarbuck =
      'Thank you very much, I am waiting here at\nStarBuck cafe.';
  static const String typeAMessage = 'Type a message';
  static const String userInfo = 'User info';
  static const String online = 'Online';
  static const String about = 'About';
  static const String helloMyName = 'Hello My name is Zilan ...';
  static const String mediaLinks = 'Media,links and doc';
  static const String muteNotifications = 'Mute notifications';
  static const String blockZilan = 'Block Zilan';
  static const String reportZilan = 'Report Zilan';
  static const String deleteChat = 'Delete chat';
  static const String chat = 'Chat';
  static const String accept = 'Accept';
  static const String reject = 'Reject';
  static const String friendRequest = 'Friend Request';
  static const String friendRequests = 'Friend Requests';
  static const String wantsToBeYourFriend = 'wants to be your friend';
  static const String friendRequestSent = 'Friend request sent!';
  static const String friendRequestAccepted = 'Friend request accepted!';
  static const String friendRequestRejected = 'Friend request rejected!';
  static const String noFriendRequests = 'No friend requests';
  static const String noUsersAvailable = 'No users available to add';
  static const String noFriendsYet = 'No friends yet';
  static const String friendsSince = 'Friends since';
  static const String pending = 'Waiting';
  static const String sendAgain = 'Send Again';
}
