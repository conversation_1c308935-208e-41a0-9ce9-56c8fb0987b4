// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyDl4ECu_VYNbL-RO8emfZx4pSY4jRaoixQ',
    appId: '1:509246975101:web:e613c33cd47583fdad9514',
    messagingSenderId: '509246975101',
    projectId: 'mood-box',
    authDomain: 'mood-box.firebaseapp.com',
    storageBucket: 'mood-box.firebasestorage.app',
    measurementId: 'G-WKRRZV8K5Q',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCxtpqdJl7jBoCSFKlc_PCNudJaAhjTTiA',
    appId: '1:509246975101:android:0601a514181d8e00ad9514',
    messagingSenderId: '509246975101',
    projectId: 'mood-box',
    storageBucket: 'mood-box.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBDMKfaA1SE3YmbvqAuagDFqdUE5FGWZU8',
    appId: '1:509246975101:ios:9755d88138c8e9b0ad9514',
    messagingSenderId: '509246975101',
    projectId: 'mood-box',
    storageBucket: 'mood-box.firebasestorage.app',
    iosBundleId: 'com.example.movieProj',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyDl4ECu_VYNbL-RO8emfZx4pSY4jRaoixQ',
    appId: '1:509246975101:web:af027587d3422287ad9514',
    messagingSenderId: '509246975101',
    projectId: 'mood-box',
    authDomain: 'mood-box.firebaseapp.com',
    storageBucket: 'mood-box.firebasestorage.app',
    measurementId: 'G-ENSQJNPYN5',
  );
}
